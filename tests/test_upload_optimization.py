#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件上传优化测试
"""
import asyncio
import time
from pathlib import Path
from typing import List, <PERSON>ple

import pytest

from controller.repository.doc import DocController


class TestUploadOptimization:
    """测试上传优化功能"""
    
    @pytest.fixture
    def sample_files(self) -> List[Tuple[Path, bytes]]:
        """创建测试文件数据"""
        files = []
        
        # 小文件 (1MB)
        small_content = b"x" * (1024 * 1024)
        files.append((Path("test/small_file.txt"), small_content))
        
        # 中等文件 (5MB)
        medium_content = b"y" * (5 * 1024 * 1024)
        files.append((Path("test/medium_file.txt"), medium_content))
        
        # 大文件 (15MB)
        large_content = b"z" * (15 * 1024 * 1024)
        files.append((Path("test/large_file.txt"), large_content))
        
        return files
    
    @pytest.mark.asyncio
    async def test_single_upload_performance(self, sample_files):
        """测试单文件上传性能"""
        path, content = sample_files[0]  # 使用小文件
        
        start_time = time.time()
        await DocController.upload_s3(path, content)
        end_time = time.time()
        
        upload_time = end_time - start_time
        file_size_mb = len(content) / (1024 * 1024)
        speed_mbps = file_size_mb / upload_time
        
        print(f"单文件上传: {file_size_mb:.2f}MB, 耗时: {upload_time:.2f}s, 速度: {speed_mbps:.2f}MB/s")
        
        # 断言上传时间合理 (应该在30秒内完成1MB文件)
        assert upload_time < 30
    
    @pytest.mark.asyncio
    async def test_batch_upload_performance(self, sample_files):
        """测试批量上传性能"""
        start_time = time.time()
        results = await DocController.upload_s3_batch(sample_files)
        end_time = time.time()
        
        upload_time = end_time - start_time
        total_size_mb = sum(len(content) for _, content in sample_files) / (1024 * 1024)
        speed_mbps = total_size_mb / upload_time
        
        print(f"批量上传: {total_size_mb:.2f}MB, 耗时: {upload_time:.2f}s, 速度: {speed_mbps:.2f}MB/s")
        
        # 断言所有文件都上传成功
        assert all(results)
        assert len(results) == len(sample_files)
        
        # 断言批量上传时间合理
        assert upload_time < 120  # 应该在2分钟内完成所有文件
    
    @pytest.mark.asyncio
    async def test_concurrent_upload_limit(self):
        """测试并发上传限制"""
        # 创建多个小文件用于测试并发
        files = []
        for i in range(10):
            content = f"test content {i}".encode() * 1000  # 约13KB每个文件
            files.append((Path(f"test/concurrent_{i}.txt"), content))
        
        start_time = time.time()
        results = await DocController.upload_s3_batch(files, max_concurrent=3)
        end_time = time.time()
        
        upload_time = end_time - start_time
        print(f"并发上传测试: 10个文件, 耗时: {upload_time:.2f}s")
        
        # 断言所有文件都上传成功
        assert all(results)
        assert len(results) == 10
    
    @pytest.mark.asyncio
    async def test_large_file_chunked_upload(self, sample_files):
        """测试大文件分块上传"""
        # 使用最大的文件进行测试
        path, content = sample_files[-1]  # 15MB文件
        
        start_time = time.time()
        await DocController.upload_s3(path, content)
        end_time = time.time()
        
        upload_time = end_time - start_time
        file_size_mb = len(content) / (1024 * 1024)
        
        print(f"大文件分块上传: {file_size_mb:.2f}MB, 耗时: {upload_time:.2f}s")
        
        # 断言大文件上传时间合理
        assert upload_time < 180  # 应该在3分钟内完成15MB文件
    
    @pytest.mark.asyncio
    async def test_upload_error_handling(self):
        """测试上传错误处理"""
        # 使用无效路径测试错误处理
        invalid_path = Path("")  # 空路径
        content = b"test content"
        
        with pytest.raises(Exception):
            await DocController.upload_s3(invalid_path, content)
    
    @pytest.mark.asyncio
    async def test_batch_upload_partial_failure(self):
        """测试批量上传部分失败的情况"""
        files = [
            (Path("test/valid_file.txt"), b"valid content"),
            (Path(""), b"invalid path"),  # 这个会失败
            (Path("test/another_valid_file.txt"), b"another valid content")
        ]
        
        results = await DocController.upload_s3_batch(files)
        
        # 应该有部分成功，部分失败
        assert len(results) == 3
        assert results[0] is True   # 第一个文件成功
        assert results[1] is False  # 第二个文件失败
        assert results[2] is True   # 第三个文件成功


if __name__ == "__main__":
    # 运行简单的性能测试
    async def simple_test():
        test_instance = TestUploadOptimization()
        
        # 创建测试数据
        small_file = (Path("test/simple_test.txt"), b"Hello World!" * 1000)
        
        print("开始简单上传测试...")
        start = time.time()
        await DocController.upload_s3(small_file[0], small_file[1])
        end = time.time()
        
        print(f"上传完成，耗时: {end - start:.2f}秒")
    
    # 运行测试
    asyncio.run(simple_test())
