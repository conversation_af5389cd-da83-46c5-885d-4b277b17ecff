#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import Query, Body

from common import g
from view import BaseView, api_description
from controller.chat.session import Session, ChatSessionType
from controller.repository import <PERSON><PERSON>, Doc
from exception import PermissionD<PERSON>yError, NotFoundError


class QAChatSessionAllView(BaseView):
    @api_description(summary="查询全部")
    async def get(self,
                  session_id: Annotated[int, Query(title="会话ID")] = None,
                  match: Annotated[str, Query(title="模糊匹配")] = None):
        sessions = await Session.get_all(
            session_id=session_id, match=match, user_id=g.user_id, session_type=ChatSessionType.QA_DOC)
        return self.response(data=sessions)


class QAChatSessionView(BaseView):
    @api_description(summary="查询会话")
    async def get(self,
                  session_id: Annotated[int, Query(title="Session ID")]):
        session_item = await Session.get_one(session_id=session_id, session_type=ChatSessionType.QA_DOC)
        return self.response(data=session_item.model_dump())

    @api_description(summary="创建会话")
    async def post(self,
                   session_type: Annotated[ChatSessionType, Body(title="知识库IDs", embed=True)] = ChatSessionType.QA_DOC):
        session_id = await Session.create(session_type=session_type)
        await g.session.commit()
        return self.response(data={"session_id": session_id})

    @api_description(summary="修改会话")
    async def put(self,
                  session_id: Annotated[int, Body(title="会话ID")],
                  session_title: Annotated[str, Body(title="会话标题")] = None):
        if not await Session.get_one(session_id=session_id, session_type=ChatSessionType.QA_DOC):
            raise NotFoundError("未找到指定的会话")

        await Session.update(session_id=session_id, title=session_title, session_type=ChatSessionType.QA_DOC)
        await g.session.commit()
        return self.response(message="修改成功")

    @api_description(summary="删除会话")
    async def delete(self,
                     session_id: Annotated[int, Body(embed=True, title="Session ID")]):
        if not await Session.get_one(session_id=session_id, session_type=ChatSessionType.QA_DOC):
            raise NotFoundError("未找到指定的会话")

        await Session.delete(session_id=session_id, session_type=ChatSessionType.QA_DOC)
        await g.session.commit()
        return self.response(message="删除成功")


class ChatMessageListView(BaseView):
    @api_description(summary="查询会话记录")
    async def get(self,
                  match: Annotated[str, Query(title="模糊匹配")] = None,
                  start: Annotated[str, Query(title="起始时间")] = None,
                  end: Annotated[str, Query(title="结束时间")] = None,
                  page: Annotated[int, Query(title="分页页数")] = 1,
                  per_page: Annotated[int, Query(title="分页容量")] = 20,
                  order_by: Annotated[str, Query(title="排序字段")] = "query_time:desc"):
        if not self.is_tenant_admin:
            raise PermissionDenyError()

        pager, chat_messages = await Session.get_chat_message_list(
            tenant_id=g.tenant_id, match=match, start=start, end=end, page=page, per_page=per_page, order_by=order_by)

        repo_ids = []
        doc_ids = []
        for msg in chat_messages:
            if msg["repo_ids"]:
                repo_ids.extend(msg["repo_ids"])
            if msg["doc_ids"]:
                doc_ids.extend(msg["doc_ids"])
        repo_id_mapping = await Repo.get_id_name_mapping(repo_ids=repo_ids, is_delete=None)
        doc_id_mapping = await Doc.get_id_name_mapping(doc_ids=doc_ids, is_delete=None)
        for msg in chat_messages:
            if msg["repo_ids"]:
                msg["repos"] = [{"repo_id": repo_id, "repo_name": repo_id_mapping.get(repo_id, "已删除知识库")} for repo_id in msg["repo_ids"]]
            else:
                msg["repos"] = []
            if msg["doc_ids"]:
                msg["docs"] = [{"doc_id": doc_id, "doc_name": doc_id_mapping.get(doc_id, "已删除文档")} for doc_id in msg["doc_ids"]]
            else:
                msg["docs"] = []

        return self.response(data=chat_messages, pager=pager)


class ChatMessageView(BaseView):
    @api_description(summary="查询会话记录")
    async def get(self,
                  request_id: Annotated[str, Query(title="请求ID")]):
        if not self.is_tenant_admin:
            raise PermissionDenyError()

        chat_messages = await Session.get_chat_message_one(request_id=request_id)
        if chat_messages["repo_ids"]:
            repo_id_mapping = await Repo.get_id_name_mapping(repo_ids=chat_messages["repo_ids"], is_delete=None)
            chat_messages["repos"] = [{"repo_id": repo_id, "repo_name": repo_id_mapping[repo_id]} for repo_id in chat_messages["repo_ids"]]
        else:
            chat_messages["repos"] = []

        if chat_messages["doc_ids"]:
            doc_id_mapping = await Doc.get_id_name_mapping(doc_ids=chat_messages["doc_ids"], is_delete=None)
            chat_messages["docs"] = [{"doc_id": doc_id, "doc_name": doc_id_mapping[doc_id]} for doc_id in chat_messages["doc_ids"]]
        else:
            chat_messages["docs"] = []

        return self.response(data=chat_messages)
