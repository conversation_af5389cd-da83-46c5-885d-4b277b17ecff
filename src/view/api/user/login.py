#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import Body

from view import BaseView, api_description
from controller.tenant import User, Auth
from controller.system import Tenant
from common.sm import gm_sm2, gm_sm4
from exception import PasswordError, ParamsCheckError


class LoginView(BaseView):
    @api_description(summary="用户登录", authentication_classes=[])
    async def post(self,
                   username: Annotated[str, Body(title="登录账号")],
                   password: Annotated[str, Body(title="帐号密码")]):
        try:
            password_plaintext = gm_sm2.decrypt(password)
            password = gm_sm4.encrypt(password_plaintext)
        except Exception:
            raise ParamsCheckError(f"密码不合法")

        current_user = await User.get_login_user(username=username, password=password)
        if current_user is None:
            raise PasswordError()

        access_token, exp = Auth.create_token(user_id=current_user["user_id"])
        await Auth.set_token_cache(user_id=current_user["user_id"], exp=exp, data=current_user)

        current_user["tenant_plan"] = await Tenant.get_plan_all(tenant_id=current_user["tenant_id"])

        return self.response(data={
            "access_token": access_token,
            "exp": exp,
            **current_user
        }, message="登录成功")


class LogoutView(BaseView):
    @api_description(summary="用户登出")
    async def post(self):
        await Auth.delete_token_cache(user_ids=[self.user_id])

        return self.response(message="登出成功")
