import json
from typing import Optional, List, Annotated

from pydantic import BaseModel, Field, model_validator

from controller.operator.chunking import RetrieveChunkModel
from controller.retriever import HybridRetriever, WebSearchConfig, SearchEngine


class RetrieverSearchResult(BaseModel):
    """检索搜索结果模型"""
    url: Annotated[str, Field(title="文档URL", description="搜索结果对应的文档唯一标识符")]
    title: Annotated[str | List[str], Field(title="标题", description="搜索结果的标题")]
    plain_content: Annotated[Optional[str], Field(title="原始内容", description="搜索结果的原始内容")]
    data_time: Annotated[str, Field(title="数据时间", description="对于上传数据可能无意义")]

    @model_validator(mode="after")
    def transform_data(self):
        if isinstance(self.title, list):
            self.title = "-".join(self.title)
        return self


async def retriever_search(query: str, enable_web_search: bool = True) -> str:
    """
    在知识库中搜索相关信息，检索与查询内容最匹配的文档片段。

    该工具可以帮助你获取系统知识库中的专业数据、历史资料、内部文档和结构化信息。
    适合需要查询内部资料、专业领域知识或历史数据分析的场景。

    Args:
        query: 搜索查询内容，应该是明确、具体的问题或关键词。
               例如"宁德时代财务状况"、"某公司投资风险分析"等。
               建议避免在query参数中使用3个以上的关键词，除非是非常必要的。
        enable_web_search: 是否启用联网搜索，默认为True。
                          如果设置为False，则只在知识库中进行搜索，不进行网络查询。

    Returns:
        str: JSON格式的搜索结果列表

    Raises:
        Exception: 当检索过程中发生错误时抛出异常
    """
    if not query.strip():
        raise ValueError("搜索查询内容不能为空")

    # 配置网络搜索
    web_search_config = None
    if enable_web_search:
        web_search_config = WebSearchConfig(
            name=SearchEngine.zhipu,
            search_config={
                "extract": False,  # 是否进入URL进行html解析/切片/入库和召回
                "count": 10  # 提取的网页数量，智谱最多支持Top50，但会过滤掉没有url的信息
            }
        )

    try:
        # 初始化混合检索器
        retriever = HybridRetriever(
            max_doc_size=20,
            topn=10,
            web_search=web_search_config
        )

        # 执行检索
        results: List[RetrieveChunkModel] = await retriever.retrieve(query=query)

        # 转换结果格式
        search_results = [
            RetrieverSearchResult(
                url=res.url if res.url else f"http://local/{res.doc_id}",
                title=res.title or res.filename or "无标题",
                plain_content=res.plain_content or "无内容",
                data_time=res.data_time or "未知时间"
            )
            for res in results
        ]

        return json.dumps(
            [result.model_dump() for result in search_results],
            ensure_ascii=False,
            indent=None
        )

    except Exception as e:
        raise Exception(f"检索过程中发生错误: {str(e)}") from e


if __name__ == "__main__":
    import asyncio

    # async def main():
    #     try:
    #         result = await retriever_search(query="宁德时代")
    #         print(len(result), result)
    #     except Exception as e:
    #         print(f"Error: {e}")
    #
    # asyncio.run(main())

    # 知识库搜索
    async def repo_search() -> List[RetrieveChunkModel]:
        retriever = HybridRetriever(
            max_doc_size=20,
            topn=10,
            doc_threshold=50  # 关联度阈值,推荐深入研究设置的较高(50)
        )
        docs = await retriever.retrieve(query="隆基绿能在西北有什么项目")
        return docs

    async def web_search() -> List[RetrieveChunkModel]:
        retriever = HybridRetriever(
            web_search=WebSearchConfig(
                name=SearchEngine.zhipu,
                search_config={
                    "extract": False,  # 是否进入URL进行html解析/切片/入库和召回
                    "count": 10  # 提取的网页数量，智谱最多支持Top50，但会过滤掉没有url的信息
                }
            ),
            repo_search=False)  # 关闭知识库检索
        docs = await retriever.retrieve(query="隆基绿能在西北有什么项目")
        return docs

    # print("知识库搜索: ", asyncio.run(repo_search()))
    # print("网络搜索: ", asyncio.run(web_search()))
