#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from enum import IntEnum

from sqlalchemy import String
from sqlalchemy.orm import Mapped, mapped_column

from model.base import BaseModel


class DocStatus(IntEnum):
    """文档状态"""
    parsing = 1  # 解析中
    parse_fail = 4  # 解析失败
    parse_success = 8  # 解析成功
    extracting = 9  # AI分析中
    extract_fail = 10  # AI分析失败
    extract_success = 11  # AI解析成功


# [RDB]
class DocModel(BaseModel):
    __tablename__ = "doc"
    __comment__ = "文档表"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, nullable=False, comment="文档ID")
    repo_id: Mapped[int] = mapped_column(nullable=False, comment="文档库ID")
    md5: Mapped[str] = mapped_column(String(120), nullable=True, comment="ES文档ID")
    name: Mapped[str] = mapped_column(String(512), nullable=False, comment="文档名称")
    path: Mapped[str] = mapped_column(String(100), nullable=False, comment="文件路径")
    tags: Mapped[list] = mapped_column(default='[]', nullable=False, comment="原文标签")
    size: Mapped[int] = mapped_column(nullable=False, default=0, comment="文档大小(字节)")
    status: Mapped[int] = mapped_column(nullable=False, default=0, comment="文档状态")
    info: Mapped[dict] = mapped_column(nullable=False, default='{}', comment="文档附加信息")


# [ES]
DOC_MAPPING = {
    "settings": {
        "number_of_shards": 1,
        "number_of_replicas": 0,
        "index.mapping.nested_objects.limit": 10000,
        "similarity": {
            "custom_bm25": {
                "type": "BM25",
                "k1": 0.5,  # 词频饱和度 默认值1.2
                "b": 0.2  # 长度归一化强度 默认0.75
            }
        },
        "analysis": {
            "filter": {
                # 停用词过滤器
                "stop_words_filter": {
                    "type": "stop",
                    "ignore_case": True,
                    "stopwords_path": "/usr/share/elasticsearch/config/stopwords.txt"
                },
                # 文本数字变化切分
                # 主要用来处理连续数字
                "word_delimiter_graph_filter": {
                    "type": "word_delimiter_graph",
                    "adjust_offsets": False  # 禁止修改偏移量
                },
                # 字符小写转换过滤器
                "lowercase_filter": {
                    "type": "lowercase"
                },
            },
            "tokenizer": {
                "ngram_verbatim_tokenizer": {
                    "type": "ngram",
                    "min_gram": 1,
                    "max_gram": 1,
                    "token_chars": []
                }
            },
            "analyzer": {
                "ik_smart": {
                    "type": "custom",
                    "tokenizer": "ik_smart",
                    "filter": [
                        "word_delimiter_graph_filter",  # 数字转换器
                        "lowercase_filter",  # 大小写转换器
                        "stop_words_filter",  # 停用词分词器
                    ]
                },
                "ik_max_word": {
                    "type": "custom",
                    "tokenizer": "ik_max_word",
                    "filter": [
                        "lowercase_filter",
                        "word_delimiter_graph_filter",
                    ]
                },
                "ngram_verbatim": {
                    "tokenizer": "ngram_verbatim_tokenizer",
                    "filter": [
                        "word_delimiter_graph_filter",  # 数字转换器
                        "lowercase_filter",  # 大小写转换器
                        "stop_words_filter",  # 停用词分词器
                    ]
                }
            },
        }
    },
    "mappings": {
        "dynamic": False,
        "properties": {
            # 文档ID
            "doc_id": {
                "type": "keyword"
            },
            # 租户ID
            "tenant_id": {
                "type": "integer"
            },
            # 知识库ID
            "repo_id": {
                "type": "integer"
            },
            # 唯一标签
            "md5": {
                "type": "keyword",
            },
            # 文件标题
            "filename": {
                "type": "text",
                "analyzer": "ik_max_word",
                "search_analyzer": "ik_smart",
                "similarity": "custom_bm25"
            },
            # 文件类型
            "doc_type": {
                "type": "keyword",
            },
            # 溯源方式
            "reference_type": {
                "type": "keyword"
            },
            # 文件状态:
            "status": {
                "type": "integer"
            },
            # 原文标签
            "tags": {
                "type": "keyword",
            },
            # 文档标题
            "title": {
                "type": "text",
                "analyzer": "ik_max_word",
                "search_analyzer": "ik_smart",
                "similarity": "custom_bm25"
            },
            # 文档来源
            "source": {
                "type": "text",
                "analyzer": "ik_max_word",
                "search_analyzer": "ik_smart",
                "similarity": "custom_bm25"
            },
            # 文档链接
            "url": {
                "type": "keyword"
            },
            # 文档作者
            "author": {
                "type": "text",
                "analyzer": "ik_max_word",
                "search_analyzer": "ik_smart",
                "similarity": "custom_bm25"
            },
            # 图标外链
            "icon": {
                "type": "keyword",
                "index": False
            },
            # 文档html(借助压缩且避免minio读取)
            "html": {
                "type": "text",
                "index": False
            },
            # 全文html token计数
            "token_counts": {
                "type": "integer"
            },
            # 文档纯文本
            "plain_text": {
                "type": "text",
                "analyzer": "ik_max_word",
                "search_analyzer": "ik_smart",
                "similarity": "custom_bm25"
            },
            # 文档切片
            "chunks": {
                "type": "nested",
                "properties": {
                    # 文档块ID
                    "cid": {
                        "type": "keyword"
                    },
                    # 文档块类型
                    "type_": {
                        "type": "keyword"
                    },
                    # 文档块在文章中的索引位置
                    "index": {
                        "type": "integer"
                    },
                    # 标题纯文本,该字段是列表
                    "title": {
                        "type": "text",
                        "analyzer": "ik_max_word",
                        "search_analyzer": "ik_smart",
                        "similarity": "custom_bm25"
                    },
                    # html文本
                    "html_content": {
                        "type": "text",
                        "analyzer": "ik_max_word",
                        "search_analyzer": "ik_smart",
                        "similarity": "custom_bm25"
                    },
                    # 纯文字文本,用于召回
                    "plain_content": {
                        "type": "text",
                        "analyzer": "ik_max_word",
                        "search_analyzer": "ik_smart",
                        "similarity": "custom_bm25",
                        "fields": {
                            "ngram": {
                                "type": "text",
                                "analyzer": "ngram_verbatim",
                            }
                        }
                    },
                    # xpath位置
                    "xpath": {
                        "type": "keyword"
                    },
                    # 长度
                    "token_counts": {
                        "type": "integer"
                    },
                    # 字符向量
                    "vector": {
                        "type": "dense_vector",
                        "dims": 1024
                    },
                    # 起始偏移量(基于origin_plain_content)
                    "start_offset": {
                        "type": "integer"
                    },
                    # 结束偏移量(基于origin_plain_content)
                    "end_offset": {
                        "type": "integer"
                    },
                    # bbox MinerU解析器支持
                    "bboxes": {
                        "type": "object",
                        "properties": {
                            "page": {"type": "integer"},  # 页数
                            "bbox": {"type": "integer", "index": False},  # bbox坐标
                            "page_size": {"type": "integer", "index": False}  # 页面大小
                        }
                    }
                }
            },
            # 关键词
            "keywords": {
                "type": "keyword"
            },
            # 抽取模型
            "extract_model": {
                "type": "object",
                "properties": {
                    "model_id": {
                        "type": "keyword"
                    },
                    "show_name": {
                        "type": "keyword"
                    },
                    "model_name": {
                        "type": "keyword"
                    }
                }
            },
            # 提取结果
            "extract_result": {
                "type": "object",
                "properties": {
                    # 主体
                    "subject": {
                        "type": "text",
                        "analyzer": "ik_max_word",
                        "search_analyzer": "ik_smart",
                        "similarity": "custom_bm25",
                        "fields": {
                            "keyword": {
                                "type": "keyword"
                            }
                        }
                    },
                    # 行业
                    "industry": {
                        "type": "keyword",
                    },
                    # 正面观点
                    "positive_view": {
                        "type": "text",
                        "analyzer": "ik_max_word",
                        "search_analyzer": "ik_smart",
                        "similarity": "custom_bm25"
                    },
                    # 正面观点关键词
                    "positive_view_keywords": {
                        "type": "keyword"
                    },
                    # 负面观点
                    "negative_view": {
                        "type": "text",
                        "analyzer": "ik_max_word",
                        "search_analyzer": "ik_smart",
                        "similarity": "custom_bm25"
                    },
                    # 负面观点关键词
                    "negative_view_keywords": {
                        "type": "keyword"
                    },
                    # 情绪因子 [-100, 100]
                    "sentiment_score": {
                        "type": "float",
                    },
                    # 舆情影响指数 [0, 100]
                    "sentiment_influence_score": {
                        "type": "float",
                    },
                    # 关键信息
                    "keywords": {
                        "type": "text",
                        "analyzer": "ik_max_word",
                        "search_analyzer": "ik_smart",
                        "similarity": "custom_bm25",
                        "fields": {
                            "keyword": {
                                "type": "keyword"
                            }
                        }
                    },
                    # 摘要
                    "abstract": {
                        "type": "text",
                        "analyzer": "ik_max_word",
                        "search_analyzer": "ik_smart",
                        "similarity": "custom_bm25"
                    },
                    # 主观分析(暂未启用)
                    "analysis": {
                        "type": "text",
                        "analyzer": "ik_max_word",
                        "search_analyzer": "ik_smart",
                        "similarity": "custom_bm25"
                    },
                    # 思考过程
                    "think_content": {
                        "type": "flattened"
                    }
                }
            },
            # 数据时间
            "data_time": {
                "type": "date",
                "format": "yyyy-MM-dd HH:mm:ss||strict_date_optional_time||epoch_millis"
            },
            # 创建时间
            "create_time": {
                "type": "date",
                "format": "yyyy-MM-dd HH:mm:ss||strict_date_optional_time||epoch_millis"
            }
        }
    }
}

ALL_REPO_INDEX = "repo_*"
WEB_DOC_INDEX = "web_doc"


def get_index(repo_ids: list[int] | None = None) -> str:
    if repo_ids is None:
        return ALL_REPO_INDEX
    return ",".join([f"repo_{rid}" for rid in repo_ids])
