#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import urllib3
from minio import Minio

from config import MINIO_URL, MINIO_ROOT_USER, MINIO_ROOT_PASSWORD, UPLOAD_CHUNK_SIZE, MAX_CONCURRENT_UPLOADS

# 配置连接池以提高并发性能
http_client = urllib3.PoolManager(
    timeout=urllib3.Timeout(connect=10, read=600),  # 连接超时10s，读取超时5分钟
    maxsize=20,  # 连接池大小，支持更多并发连接
    retries=urllib3.Retry(
        total=3,  # 总重试次数
        backoff_factor=0.5,  # 重试间隔递增因子
        status_forcelist=[500, 502, 503, 504],  # 需要重试的HTTP状态码
        allowed_methods=["PUT", "POST", "GET"]  # 允许重试的HTTP方法
    )
)

minio_client = Minio(
    endpoint=MINIO_URL,
    access_key=MINIO_ROOT_USER,
    secret_key=MINIO_ROOT_PASSWORD,
    secure=False,
    http_client=http_client
)

repository_bucket = "repository"

# 导出配置常量供其他模块使用
__all__ = ['minio_client', 'repository_bucket', 'UPLOAD_CHUNK_SIZE', 'MAX_CONCURRENT_UPLOADS']